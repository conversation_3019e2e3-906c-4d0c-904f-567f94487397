{"name": "kodaze-admin-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.2.1", "@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "@mui/x-data-grid": "^8.11.0", "@mui/x-date-pickers": "^8.11.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.12", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-redux": "^9.2.0", "react-router": "^7.8.2", "tailwindcss": "^4.1.12", "yup": "^1.7.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/react-redux": "^7.1.34", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}