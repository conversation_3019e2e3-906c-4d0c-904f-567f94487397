import {
  Tool<PERSON>,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Box,
} from "@mui/material";
import { Link } from "react-router";

const navItems = [
  { label: "Articles", path: "/articles" },
  { label: "Categories", path: "/categories" },
  { label: "Tags", path: "/tags" },
  { label: "Subscriptions", path: "/subscriptions" },
];

function CustomDrawer({ drawerWidth }: { drawerWidth: number }) {
  return (
    <Box sx={{ width: drawerWidth }}>
      <Toolbar />
      <List>
        {navItems.map((item) => (
          <ListItem key={item.path} disablePadding>
            <ListItemButton component={Link} to={item.path}>
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );
}

export default CustomDrawer;
