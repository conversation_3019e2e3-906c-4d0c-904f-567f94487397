import TableWithControls from "../../../shared/components/TableWithControls";
import CategoriesFilter from "../components/CategoriesFilter";

interface Article {
  id: number;
  title: string;
  slug: string;
  category: string;
  views: number;
}

export default function Articles() {
  const rows: Article[] = [
    { id: 1, title: "Intro to React", slug: "intro-react", category: "Tech", views: 120 },
    { id: 2, title: "AI Trends", slug: "ai-trends", category: "Science", views: 340 },
  ];

  return (
    <TableWithControls<Article>
      columns={["id", "title", "slug", "category", "views"]}
      rows={rows}
      totalCount={50}
      FilterBar={
        <CategoriesFilter />
      }
    />
  );
}
