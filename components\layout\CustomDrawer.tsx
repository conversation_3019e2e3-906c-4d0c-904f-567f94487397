import {
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
} from "@mui/material";
import InboxIcon from "@mui/icons-material/MoveToInbox";
import MailIcon from "@mui/icons-material/Mail";
import React from "react";
import Image from "next/image";
import Link from "next/link";

function CustomDrawer() {
  return (
    <div>
      <Toolbar component={Link} href={"/dashboard"}>
        <Image
          src="/kodaze.png"
          alt="Logo"
          width={500}
          height={300}
          priority
          className="h-[30] w-auto"
        />
      </Toolbar>
      <Divider />
      <List>
        {["Articles", "Categories", "Tags", "Subscriptions"].map(
          (text, index) => (
            <ListItem key={text} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  {index % 2 === 0 ? <InboxIcon /> : <MailIcon />}
                </ListItemIcon>
                <ListItemText primary={text} />
              </ListItemButton>
            </ListItem>
          )
        )}
      </List>
    </div>
  );
}

export default CustomDrawer;
