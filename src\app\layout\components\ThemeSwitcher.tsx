import { IconButton } from "@mui/material";
import { DarkMode, LightMode } from "@mui/icons-material";
import useThemeContext from "../hooks/useThemeContext";

export default function ThemeSwitcher() {
  const { mode, setMode } = useThemeContext()

  return (
    <IconButton onClick={() => setMode(mode === "light" ? "dark" : "light")} color="inherit">
      {mode === "light" ? <DarkMode /> : <LightMode />}
    </IconButton>
  );
}
