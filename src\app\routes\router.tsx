import { createBrowserRouter } from "react-router";
import { articlesRoutes } from "./articlesRoutes";
import { tagsRoutes } from "./tagsRoutes";
import { categoriesRoutes } from "./categoriesRoutes";
import { subscribersRoutes } from "./subscribersRoutes";
import DashboardLayout from "../layout/DashboardLayout";

export const router = createBrowserRouter([
  {
    path: "/login",
    element: <div>Login</div>,
  },
  {
    path: "/",
    element: <DashboardLayout />,
    children: [
      {
        index: true,
        element: <div>Home</div>,
      },
      ...articlesRoutes,
      ...categoriesRoutes,
      ...tagsRoutes,
      ...subscribersRoutes,
    ],
  },
]);
