import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./app/App.tsx";
import { RouterProvider } from "react-router";
import { router } from "./app/routes/router.tsx";
import { Provider } from "react-redux";
import { store } from "./app/store/store.ts";

createRoot(document.getElementById("root")!).render(
  <Provider store={store}>
    <App>
      <RouterProvider router={router} />
    </App>
  </Provider>
);
