import { Stack, TextField } from "@mui/material";
import { useSearchParams } from "react-router";

function CategoriesFilter() {
  const [params, setParams] = useSearchParams();

  const search = params.get("search") ?? "";
  const category = params.get("category") ?? "";
  const tag = params.get("tag") ?? "";

  const handleChange = (key: string, value: string) => {
    setParams({
      ...Object.fromEntries(params),
      [key]: value,
      page: "0", // reset page on filter change
    });
  };

  return (
    <Stack direction={{ xs: "column", md: "row" }} spacing={2}>
      <TextField
        placeholder="Search title..."
        value={search}
        onChange={(e) => handleChange("search", e.target.value)}
        sx={{ minWidth: 160 }}
      />
      <TextField
        placeholder="Search category..."
        value={category}
        onChange={(e) => handleChange("category", e.target.value)}
        sx={{ minWidth: 160 }}
      />

      <TextField
        placeholder="Search tag..."
        value={tag}
        onChange={(e) => handleChange("tag", e.target.value)}
        sx={{ minWidth: 160 }}
      />
    </Stack>
  );
}

export default CategoriesFilter;
