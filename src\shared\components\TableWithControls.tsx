import React from "react";
import {
  Box,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Paper,
  TablePagination,
  IconButton,
} from "@mui/material";
import { Edit, Delete } from "@mui/icons-material";
import { useSearchParams } from "react-router";

interface Props<T> {
  columns: (keyof T)[];
  rows: T[];
  totalCount?: number; // show pagination only if defined
  FilterBar?: React.ReactNode; // slot for custom filters
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function TableWithControls<T extends Record<string, any>>({
  columns,
  rows,
  totalCount,
  FilterBar,
}: Props<T>) {
  const [searchParams, setSearchParams] = useSearchParams();

  const page = parseInt(searchParams.get("page") ?? "0", 10);
  const rowsPerPage = parseInt(searchParams.get("limit") ?? "5", 10);

  const handleChangePage = (_: unknown, newPage: number) => {
    setSearchParams({
      ...Object.fromEntries(searchParams),
      page: String(newPage),
      limit: String(rowsPerPage),
    });
  };

  const handleChangeRowsPerPage = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchParams({
      ...Object.fromEntries(searchParams),
      page: "0",
      limit: e.target.value,
    });
  };

  return (
    <Box>
      {FilterBar && <Box sx={{ mb: 2 }}>{FilterBar}</Box>}

      <TableContainer
        component={Paper}
        sx={{
          width: "100%",
          overflowX: "auto",
          "& table": {
            minWidth: 600,
          },
        }}
      >
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {columns.map((col) => (
                <TableCell key={String(col)}>{String(col)}</TableCell>
              ))}
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row, i) => (
              <TableRow key={i}>
                {columns.map((col) => (
                  <TableCell key={String(col)}>{row[col]}</TableCell>
                ))}
                <TableCell align="center">
                  <IconButton>
                    <Edit />
                  </IconButton>
                  <IconButton>
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {typeof totalCount === "number" && (
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25]}
        />
      )}
    </Box>
  );
}
