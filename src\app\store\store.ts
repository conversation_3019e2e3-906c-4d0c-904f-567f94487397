import { configureStore } from "@reduxjs/toolkit";
// import authReducer from "@/redux/features/authSlice";
// import { userApiSlice } from "@/redux/services/userApiSlice";

// const clearStateMiddleware =
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   (store: any) => (next: (action: any) => void) => (action: any) => {
//     const logoutFulfilled = `${userApiSlice.reducerPath}/logoutUser/fulfilled`;
//     if (action.type === logoutFulfilled) {
//       store.dispatch(userApiSlice.util.resetApiState());
//     }
//     return next(action);
//   };

export const store = configureStore({
  reducer: {
    // auth: authReducer,
    // [userApiSlice.reducerPath]: userApiSlice.reducer,
  },
  // middleware: (getDefaultMiddleware) =>
  //   getDefaultMiddleware().concat(
  //     userApiSlice.middleware,
  //     clearStateMiddleware
  //   ),
});

// 🔹 Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
