import { useState, useEffect } from "react";
import { ThemeProvider, createTheme, CssBaseline } from "@mui/material";
import type { ThemeOptions } from "@mui/material";
import { darkThemeOptions, lightThemeOptions } from "./theme/theme";
import { ThemeContext } from "./layout/context/ThemeContext";

export default function App({ children }: { children: React.ReactNode }) {
  const getSystemTheme = (): "light" | "dark" => {
    return matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light";
  };

  const [mode, setMode] = useState<"light" | "dark">(getSystemTheme);

  useEffect(() => {
    const mediaQuery = matchMedia("(prefers-color-scheme: dark)");
    const handleChange = (e: MediaQueryListEvent) => {
      setMode(e.matches ? "dark" : "light");
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  const theme = createTheme(
    (mode === "light" ? lightThemeOptions : darkThemeOptions) as ThemeOptions
  );

  return (
    <ThemeContext.Provider value={{ mode, setMode }}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  );
}
